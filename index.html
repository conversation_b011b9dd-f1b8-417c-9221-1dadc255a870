<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>江南烟雨 - 交互式意境生成器</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="container">
        <!-- Three.js 渲染容器 -->
        <canvas id="three-canvas"></canvas>
        
        <!-- 控制面板 -->
        <div id="controls-panel">
            <div class="control-group">
                <label for="rain-intensity">雨量强度</label>
                <input type="range" id="rain-intensity" min="0" max="100" value="30">
                <span id="rain-value">30</span>
            </div>
            
            <div class="control-group">
                <label for="wind-strength">风力强度</label>
                <input type="range" id="wind-strength" min="0" max="100" value="20">
                <span id="wind-value">20</span>
            </div>
            
            <div class="control-group">
                <label for="mist-density">雾气浓度</label>
                <input type="range" id="mist-density" min="0" max="100" value="40">
                <span id="mist-value">40</span>
            </div>
        </div>
        
        <!-- 三生石文字输入面板 -->
        <div id="inscription-panel" class="hidden">
            <div class="panel-content">
                <h3>在三生石上刻下您的心愿</h3>
                <textarea id="inscription-text" placeholder="请输入您想刻在三生石上的文字..."></textarea>
                <div class="panel-buttons">
                    <button id="inscribe-btn">刻印</button>
                    <button id="cancel-btn">取消</button>
                </div>
            </div>
        </div>
        
        <!-- 加载提示 -->
        <div id="loading-screen">
            <div class="loading-content">
                <h2>江南烟雨</h2>
                <p>正在加载意境...</p>
                <div class="loading-spinner"></div>
            </div>
        </div>
        
        <!-- 信息提示 -->
        <div id="info-panel">
            <div class="info-item">
                <span class="icon">🖱️</span>
                <span>拖拽鼠标控制视角</span>
            </div>
            <div class="info-item">
                <span class="icon">💨</span>
                <span>移动鼠标感受风的力量</span>
            </div>
            <div class="info-item">
                <span class="icon">🗿</span>
                <span>点击三生石刻下心愿</span>
            </div>
        </div>
    </div>
    
    <!-- Three.js 库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/controls/OrbitControls.js"></script>
    
    <!-- 主应用脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/particles.js"></script>
    <script src="js/water.js"></script>
    <script src="js/environment.js"></script>
    <script src="js/stone.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
