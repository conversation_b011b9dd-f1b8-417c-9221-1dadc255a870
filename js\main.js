// 江南烟雨 - 主应用文件

class JiangnanScene {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        
        // 系统组件
        this.particleSystem = null;
        this.waterSystem = null;
        this.environment = null;
        this.stoneSystem = null;
        
        // 交互状态
        this.isTimeFreezed = false;
        this.windStrength = 0.2;
        this.rainIntensity = 0.3;
        this.mistDensity = 0.4;
        this.mousePosition = new THREE.Vector2();
        
        // 性能监控
        this.performanceMonitor = Utils.createPerformanceMonitor();
        
        this.init();
    }
    
    init() {
        this.createScene();
        this.createCamera();
        this.createRenderer();
        this.createLights();
        this.createControls();
        this.setupEventListeners();
        this.initSystems();
        this.startRenderLoop();
        this.hideLoadingScreen();
    }
    
    createScene() {
        this.scene = new THREE.Scene();
        
        // 设置雾效果，营造江南朦胧感
        this.scene.fog = new THREE.Fog(0x87CEEB, 10, 100);
        
        // 设置背景色（天空色）
        this.scene.background = new THREE.Color(0x87CEEB);
    }
    
    createCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        
        // 设置相机初始位置，俯视江南水乡
        this.camera.position.set(0, 15, 25);
        this.camera.lookAt(0, 0, 0);
    }
    
    createRenderer() {
        const canvas = document.getElementById('three-canvas');
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas,
            antialias: true,
            alpha: true 
        });
        
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;
    }
    
    createLights() {
        // 环境光 - 模拟阴天的柔和光线
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // 主光源 - 模拟透过云层的阳光
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -50;
        directionalLight.shadow.camera.right = 50;
        directionalLight.shadow.camera.top = 50;
        directionalLight.shadow.camera.bottom = -50;
        this.scene.add(directionalLight);
        
        // 补充光源 - 模拟天空散射光
        const hemisphereLight = new THREE.HemisphereLight(0x87CEEB, 0x362d1d, 0.3);
        this.scene.add(hemisphereLight);
    }
    
    createControls() {
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.screenSpacePanning = false;
        this.controls.minDistance = 5;
        this.controls.maxDistance = 100;
        this.controls.maxPolarAngle = Math.PI / 2;
        this.controls.autoRotate = true;
        this.controls.autoRotateSpeed = 0.5;
    }
    
    setupEventListeners() {
        // 窗口大小调整
        window.addEventListener('resize', () => this.onWindowResize());
        
        // 鼠标移动事件 - 控制风力
        window.addEventListener('mousemove', (event) => this.onMouseMove(event));
        
        // 控制面板事件
        this.setupControlPanelEvents();
        
        // 三生石交互事件
        this.setupStoneInteractionEvents();
    }
    
    setupControlPanelEvents() {
        const rainSlider = document.getElementById('rain-intensity');
        const windSlider = document.getElementById('wind-strength');
        const mistSlider = document.getElementById('mist-density');
        
        rainSlider.addEventListener('input', (e) => {
            this.rainIntensity = e.target.value / 100;
            document.getElementById('rain-value').textContent = e.target.value;
            if (this.particleSystem) {
                this.particleSystem.setRainIntensity(this.rainIntensity);
            }
        });
        
        windSlider.addEventListener('input', (e) => {
            this.windStrength = e.target.value / 100;
            document.getElementById('wind-value').textContent = e.target.value;
        });
        
        mistSlider.addEventListener('input', (e) => {
            this.mistDensity = e.target.value / 100;
            document.getElementById('mist-value').textContent = e.target.value;
            if (this.particleSystem) {
                this.particleSystem.setMistDensity(this.mistDensity);
            }
        });
    }
    
    setupStoneInteractionEvents() {
        const inscriptionPanel = document.getElementById('inscription-panel');
        const inscribeBtn = document.getElementById('inscribe-btn');
        const cancelBtn = document.getElementById('cancel-btn');
        
        inscribeBtn.addEventListener('click', () => {
            const text = document.getElementById('inscription-text').value;
            if (text.trim()) {
                this.stoneSystem.inscribeText(text);
                inscriptionPanel.classList.add('hidden');
                this.resumeTime();
            }
        });
        
        cancelBtn.addEventListener('click', () => {
            inscriptionPanel.classList.add('hidden');
            this.resumeTime();
        });
    }
    
    onMouseMove(event) {
        // 更新鼠标位置，用于风力计算
        this.mousePosition.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mousePosition.y = -(event.clientY / window.innerHeight) * 2 + 1;
        
        // 根据鼠标位置计算风力强度
        const mouseDistance = this.mousePosition.length();
        this.windStrength = Math.min(mouseDistance * 0.5, 1.0);
    }
    
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    initSystems() {
        // 初始化各个系统
        this.environment = new Environment(this.scene);
        this.waterSystem = new WaterSystem(this.scene);
        this.particleSystem = new ParticleSystem(this.scene);
        this.stoneSystem = new StoneSystem(this.scene, this.camera, this.renderer);
        
        // 设置系统间的交互
        this.stoneSystem.onStoneClick = () => this.freezeTime();
    }
    
    freezeTime() {
        this.isTimeFreezed = true;
        this.controls.autoRotate = false;
        document.getElementById('inscription-panel').classList.remove('hidden');
        
        // 通知所有系统时间冻结
        eventManager.emit('timeFreeze', true);
    }
    
    resumeTime() {
        this.isTimeFreezed = false;
        this.controls.autoRotate = true;
        
        // 通知所有系统时间恢复
        eventManager.emit('timeFreeze', false);
    }
    
    update() {
        const deltaTime = 0.016; // 假设60fps
        
        if (!this.isTimeFreezed) {
            // 更新各个系统
            if (this.particleSystem) {
                this.particleSystem.update(deltaTime, this.windStrength);
            }
            
            if (this.waterSystem) {
                this.waterSystem.update(deltaTime, this.windStrength);
            }
            
            if (this.environment) {
                this.environment.update(deltaTime, this.windStrength);
            }
        }
        
        if (this.stoneSystem) {
            this.stoneSystem.update(deltaTime);
        }
        
        // 更新控制器
        this.controls.update();
    }
    
    render() {
        this.renderer.render(this.scene, this.camera);
    }
    
    startRenderLoop() {
        const animate = () => {
            requestAnimationFrame(animate);
            
            this.update();
            this.render();
            
            // 更新性能监控
            this.performanceMonitor.update();
        };
        
        animate();
    }
    
    hideLoadingScreen() {
        setTimeout(() => {
            const loadingScreen = document.getElementById('loading-screen');
            loadingScreen.classList.add('hidden');
        }, 2000);
    }
}

// 应用启动
window.addEventListener('DOMContentLoaded', () => {
    window.jiangnanScene = new JiangnanScene();
});
